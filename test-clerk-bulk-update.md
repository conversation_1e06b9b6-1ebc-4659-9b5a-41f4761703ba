# Clerk Bulk Status Update Implementation

## Summary
Added bulk status update functionality for Department Clerks similar to what exists for Team Leaders and Shift Leaders.

## Changes Made

### 1. Store Interface Update (teamLeaderStore.ts)
- Added `updateClerkSubordinateStatusBulk` function to the interface
- Function signature: `(clerkInstantId: string, statusCode: string, subordinateIds: string[]) => Promise<void>`

### 2. Store Implementation (teamLeaderStore.ts)
- Implemented `updateClerkSubordinateStatusBulk` function
- API endpoint: `/api/v1/clerks/clerk_instant/{clerkInstantId}/subordinates/status/bulk`
- Request payload: `{ statusCode, subordinateIds }`
- Local state update logic similar to single status update but for multiple subordinates
- Toast notifications for success/error

### 3. UI Component Update (MyTeamHeader.tsx)
- Added import for `updateClerkSubordinateStatusBulk`
- Added bulk action handling in `handleSubmitAction` function for DEPARTEMENT_CLERK role
- Added bulk actions UI section for Department Clerk (similar to Shift Leader)
- U<PERSON> shows when `selectedWorkers.size > 0` and user is DEPARTEMENT_CLERK
- Dropdown with Present/Absent options
- Submit/Cancel buttons

## API Endpoint
```
POST /api/v1/clerks/clerk_instant/{clerkInstantId}/subordinates/status/bulk
```

### Request Body
```json
{
  "statusCode": "P" | "AB",
  "subordinateIds": ["uuid1", "uuid2", "uuid3"]
}
```

## Usage Flow
1. Department Clerk selects multiple subordinates using checkboxes
2. Bulk action UI appears showing selected count
3. Clerk selects action (Present/Absent) from dropdown
4. Clicks Submit button
5. API call is made to update all selected subordinates
6. Local state is updated for immediate UI feedback
7. Success/error toast is shown

## Testing
To test this functionality:
1. Login as Department Clerk
2. Navigate to clocking-ih or clocking-is tab
3. Select multiple subordinates using checkboxes
4. Bulk action UI should appear
5. Select Present or Absent from dropdown
6. Click Submit to update all selected subordinates

## Notes
- Uses the same local update pattern as other bulk operations
- Follows existing code patterns for consistency
- Error handling with toast notifications
- Clears selections after successful update
