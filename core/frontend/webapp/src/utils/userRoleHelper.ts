import { UserR<PERSON> } from "@/enum/rolesEnum";
import { SimpleMockUser } from "@/data/mockUsers";

/**
 * Resolves the UserRole enum from a user object.
 * The role in the mock data is a lowercase string,
 * so it needs to be mapped to the corresponding enum value.
 *
 * @param user - The mock user object.
 * @returns The resolved UserRole enum value.
 */
export const resolveUserRole = (user: SimpleMockUser | null): UserRole => {
  const roleString = user?.role?.toLowerCase();

  switch (roleString) {
    case "shift leader":
    case "shift leader 2":
      return UserRole.SHIFT_LEADER;

    case "team leader":
    case "team leader 1":
    case "team leader 2":
    case "team leader 3":
    case "team leader 4":
      return UserRole.TEAM_LEADER;

    case "quality supervisor":
    case "quality supervisor 2":
      return UserRole.QUALITY_SUPERVISOR;

    case "superadmin":
      return UserRole.SuperAdmin;

    case "department clerk":
      return UserRole.DEPARTEMENT_CLERK;

    case "department manager":
      return UserRole.DEPARTEMENT_MANAGER;

    case "tks responsible":
    case "tks_responsible":
      return UserRole.TKS_RESPONSIBLE;

    case "training responsible":
    case "training_responsible":
      return UserRole.TRAINING_RESPONSIBLE;

    case "trainer":
      return UserRole.TRAINER;

    case "operator":
      return UserRole.OPERATOR;

    case "user":
      return UserRole.USER;

    // Add other role mappings here as needed
    // ...

    default:
      return UserRole.TEAM_LEADER; // Default to Team Leader if no match
  }
};
