"use client";

import { useState, useCallback, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useAttendanceStore } from "../store/myTeamStore";
import { useTeamLeaderStore } from "../store/teamLeaderStore";
import CustomSelect from "@/components/common/CustomSelect";
import CustomIcon from "@/components/common/CustomIcons";
import { DatePicker } from "@/components/ui/datePicker";
import { UserRole } from "@/enum/rolesEnum";
import { useMockAuthStore } from "@/store/mockAuthStore";
import { resolveUserRole } from "@/utils/userRoleHelper";
import ValidationDialog from "@/components/common/ValidationDialog";
import { IconButton, ReusableButton } from "@/components/common/CustomButtons";
import { ShiftLeaderTabs } from "./ShiftLeaderTabs";
import { DepartmentClerkTabs } from "./DepartmentClerkTabs";
import { MyTeamTable } from "./MyTeamTable";
import { SendForReviewDialog } from "./dialogs/SendForReviewDialog";
import { ReplacementFeedbackDialog } from "./dialogs/EvaluationDialog";
import { BulkRequestDialog } from "./dialogs/BulkRequestDialog";
import { RequestFormDialog } from "./dialogs/RequestFormDialog";
import MatchingInterface from "./ShiftLeaderBackup";
import useReplacementStore from "../store/replacementStore";
import DepartmentBackup from "./DepartmentBackup";
import { toast } from "@/hooks/use-toast";
import api from "@/lib/axios";

const createIcon = (
  name:
    | "fileText"
    | "user"
    | "error"
    | "bell"
    | "circleUser"
    | "reassign"
    | "project"
    | "family"
    | "lineChart"
    | "areaChart"
    | "team"
    | "customer"
    | "meDefinition"
    | "play"
    | "doubleCheck",
  fill: string
) => (
  <span className="flex items-center">
    <CustomIcon
      name={name}
      className="mr-2"
      style={{
        width: "17px",
        height: "18px",
        fill,
      }}
    />
  </span>
);

const ProjectIcons = createIcon("project", "#000000");
const FamilyIcons = createIcon("family", "#9E7A00");
const LineChartIcons = createIcon("lineChart", "#4CAF50");
const AreaIcons = createIcon("areaChart", "#c59800");
const TeamIcons = createIcon("team", "#8804A1");
const CustomerIcons = createIcon("customer", "#ffb72f");
const PlayIcons = (
  <span className="flex items-center">
    <CustomIcon
      name="play"
      className="mr-2"
      style={{
        width: "25px",
        height: "25px",
        fill: "#ffffff",
      }}
    />
  </span>
);

const addIcon = (
  <CustomIcon
    name="circleAdd"
    style={{
      width: "20px",
      height: "18px",
      fill: "#ffffff",
    }}
  />
);

export function MyTeamHeader() {
  const [currentTime, setCurrentTime] = useState(new Date());

  const { currentUser: user } = useMockAuthStore();


  const userRole: UserRole = resolveUserRole(user);




  const getStatusOptions = () => {

    

    let shfitTypeLabel = shiftTypes.find((s) => s.shiftInstantId == selectedShiftType)?.shiftType
    if (shfitTypeLabel && shfitTypeLabel !== "NORMAL") {
      return [
        { value: "X", label: "X" },
        { value: "Y", label: "Y" },
        { value: "W", label: "W" },
      ];
    }
    return [
      { value: "P", label: "Present" },
      { value: "AB", label: "Absent" },
    ];
  };

  const {
    isVisualCheckActive,
    visualCheckTimer,
    isMyBackupBasketActive,
    myBackupBasketTimer,
    isDepartmentBackupBasketActive,
    departmentBackupBasketTimer,
    filterMfgOnly,
    shiftStartTime,
    startVisualCheck,
    // finishVisualCheck,
    setVisualCheckTimer,
    startMyBackupBasket,
    setMyBackupBasketTimer,
    startDepartmentBackupBasket,
    setDepartmentBackupBasketTimer,
    toggleMfgFilter,
    approveAttendanceSheet,
    setClockingCategory,
    selectedValidationTeam,
    // Team leader filter state and actions
    teamLeaderFilters,
    fetchFilteredOptions,
    setSelectedCustomer,
    setSelectedProject,
    setSelectedFamily,
    setSelectedValueStream,
    setSelectedArea,
    setSelectedTeam,
    currentDate,
    setCurrentDate,
  } = useAttendanceStore();

  // Team Leader specific store
  const {
    fetchStatusMonitoring,
    fetchShiftLeaderStatusMonitoring,
    statusMonitoringData,
    updateShiftStatus,
    fetchStatusInfo,
    statusInfoList,
    connectToStatusMonitoring,
    disconnectFromStatusMonitoring,
    updateOperatorAttendanceStatusBulk,
    // Fetch teamLeadersShiftTypes from the store
    teamLeadersShiftTypes,
    operatorsShiftTypes,
    setTeamLeaderShiftType,
    setOperatorShiftType,
    // DEPARTMENT_CLERK specific
    clerkShiftTypes,
    fetchClerkShiftTypes,
    isClerkShiftTypesLoading,
    clerkStatusMonitoringData,
    fetchClerkStatusMonitoring,
    isClerkStatusMonitoringLoading,
    updateClerkSubordinateStatusBulk,
  } = useTeamLeaderStore();
  const [isValidationDialogOpen, setIsValidationDialogOpen] = useState(false);
  const [isSendForReviewDialogOpen, setIsSendForReviewDialogOpen] =
    useState(false);
  const getShiftLeaderTabCounts = useAttendanceStore(
    (s) => s.getShiftLeaderTabCounts
  );
  const getDepartmentClerkTabCounts = useAttendanceStore(
    (s) => s.getDepartmentClerkTabCounts
  );

  const tabCounts = useMemo(
    () => getShiftLeaderTabCounts(),
    [getShiftLeaderTabCounts]
  );
  const departmentClerkTabCounts = useMemo(
    () => getDepartmentClerkTabCounts(),
    [getDepartmentClerkTabCounts]
  );
  const [activeTab, setActiveTab] = useState(() => {
    // Set default tab based on user role
    if (
      userRole === UserRole.DEPARTEMENT_CLERK ||
      userRole === UserRole.DEPARTEMENT_MANAGER
    ) {
      return "clocking-ih";
    }
    return "my-teamleaders";
  });
  const [departmentRefreshKey, setDepartmentRefreshKey] = useState(0);
  const [selectedWorkers, setSelectedWorkers] = useState<Set<string>>(
    new Set()
  );

  const [isEvaluationDialogOpen, setIsEvaluationDialogOpen] = useState(false);

  // Bulk Request Dialog States
  const [isBulkRequestDialogOpen, setIsBulkRequestDialogOpen] = useState(false);
  const [isBulkRequestFormDialogOpen, setIsBulkRequestFormDialogOpen] =
    useState(false);
  const [selectedBulkRequestType, setSelectedBulkRequestType] = useState<
    | {
        id: string;
        title: string;
      }
    | undefined
  >(undefined);
  const [selectedBulkOperators, setSelectedBulkOperators] = useState<string[]>(
    []
  );

  const [selectedShiftType, setSelectedShiftType] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedTeamLeaderShiftType, setSelectedTeamLeaderShiftType] =
    useState(teamLeadersShiftTypes.length > 0 ? teamLeadersShiftTypes[0] : "");

  const [operatorshiftType, setOperatorshiftType] = useState(
    operatorsShiftTypes.length > 0 ? operatorsShiftTypes[0] : ""
  );

  const { shiftTypes, isShiftTypesLoading, fetchShiftTypes } =
    useTeamLeaderStore();

  useEffect(() => {
    if (userRole === UserRole.SHIFT_LEADER) {
          const loadShiftTypes = async () => {
      try {
        const newShiftTypes = await fetchShiftTypes(selectedDate);


        console.log("newShiftTypes", newShiftTypes);

        if (newShiftTypes && newShiftTypes.length > 0) {
          console.log("New shift types:", newShiftTypes);
          const currentShiftExists = newShiftTypes.some(
            (shift) => shift.shiftInstantId === selectedShiftType
          );
          console.log("Current shift exists:", currentShiftExists);
          if (!currentShiftExists) {
            setSelectedShiftType(newShiftTypes[0].shiftInstantId);
          }
        } else {
          setSelectedShiftType("");
        }
      } catch (error) {
        console.error("Error in shift types effect:", error);
      }
    };

    loadShiftTypes();
    }

  }, [selectedDate, selectedShiftType, fetchShiftTypes]);

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      const dateChanged =
        date.toISOString().split("T")[0] !==
        selectedDate.toISOString().split("T")[0];
      setSelectedDate(date);
      setCurrentDate(date); 


      if (dateChanged) {
        fetchShiftTypes(date);
      }
    }
  };

  const [selectedAction, setSelectedAction] = useState<string>("");

  const getAvailableStatuses = useCallback(() => {
    if (selectedShiftType && selectedShiftType !== "NORMAL") {
      return [
        { value: "X", label: "Status X" },
        { value: "Y", label: "Status Y" },
        { value: "W", label: "Status W" },
      ];
    }
    return [
      { value: "present", label: "Present" },
      { value: "absent", label: "Absent" },
    ];
  }, [selectedShiftType]);

  const [isShiftStarted, setIsShiftStarted] = useState(false);
  const [hasOperatorsSent, setHasOperatorsSent] = useState(false);

  const { sendBackupToShiftLeader, isSendingBackup, clearSendBackupError } =
    useReplacementStore();

  const workers = useAttendanceStore((s) => s.workers);
  const filteredWorkers = useMemo(() => {
    let filtered = workers;
    if (filterMfgOnly) {
      filtered = filtered.filter(
        (worker) => worker.role.toLowerCase() === "mfg structure"
      );
    }
    if (
      userRole === UserRole.DEPARTEMENT_CLERK ||
      userRole === UserRole.DEPARTEMENT_MANAGER
    ) {
      if (activeTab === "clocking-ih") {
        filtered = filtered.filter((worker) => worker.category === "IH");
      } else if (activeTab === "clocking-is") {
        filtered = filtered.filter((worker) => worker.category === "IS");
      }
    }
    return filtered;
  }, [workers, filterMfgOnly, userRole, activeTab]);

  useEffect(() => {
    if (userRole === UserRole.TEAM_LEADER) {

    
      fetchFilteredOptions({});
    }
  }, [userRole, fetchFilteredOptions]);

  useEffect(() => {
    if (userRole === UserRole.TEAM_LEADER && selectedShiftType) {
      const endDate = currentDate.toISOString().split("T")[0];
      fetchStatusMonitoring(endDate, selectedShiftType, filterMfgOnly);
    }
  }, [
    selectedShiftType,
    currentDate,
    userRole,
    fetchStatusMonitoring,
    filterMfgOnly,
  ]);


  useEffect(() => {
    if (userRole === UserRole.SHIFT_LEADER) {
      const endDate = currentDate.toISOString().split("T")[0];
      fetchShiftLeaderStatusMonitoring(endDate);
    }
  }, [userRole, currentDate, fetchShiftLeaderStatusMonitoring]);

  useEffect(() => {
    if (userRole === UserRole.DEPARTEMENT_CLERK) {


      console.log('clerk_currentDate' ,  currentDate)
      const loadClerkData = async () => {
        try {
          const clerkShiftTypesData = await fetchClerkShiftTypes(currentDate);

          const clerkInstantId = clerkShiftTypesData.length > 0 ? clerkShiftTypesData[0].clerk_instant_id : undefined;
          await fetchClerkStatusMonitoring(currentDate, clerkInstantId);
        } catch (error) {
          console.error("Failed to load clerk data:", error);
        }
      };

      loadClerkData();
    }
  }, [userRole, currentDate, fetchClerkShiftTypes, fetchClerkStatusMonitoring]);

  useEffect(() => {
    if (userRole === UserRole.DEPARTEMENT_CLERK && selectedShiftType && clerkShiftTypes.length > 0) {
      const selectedClerkShift = clerkShiftTypes.find(shift => shift.clerk_instant_id === selectedShiftType);
      if (selectedClerkShift) {
        fetchClerkStatusMonitoring(currentDate, selectedClerkShift.clerk_instant_id);
      }
    }
  }, [userRole, selectedShiftType, clerkShiftTypes, currentDate, fetchClerkStatusMonitoring]);

  // Fetch team data for team leader - show all teams initially
  useEffect(() => {
    if (userRole === UserRole.TEAM_LEADER) {
      fetchStatusInfo(); // Fetch available status options

      // Setup SignalR connection for real-time updates
      const initializeSignalR = async () => {
        try {
          await connectToStatusMonitoring();
          // Note: setupStatusMonitoringListeners is already called inside connectToStatusMonitoring
        } catch (error) {
          console.error("Failed to initialize SignalR:", error);
        }
      };

      initializeSignalR();
    }

    // Cleanup function
    return () => {
      if (userRole === UserRole.TEAM_LEADER) {
        disconnectFromStatusMonitoring();
      }
    };
  }, [
    userRole,
    currentDate,
    fetchStatusMonitoring,
    fetchStatusInfo,
    connectToStatusMonitoring,
    disconnectFromStatusMonitoring,
  ]);

  // Join SignalR groups when status monitoring data is available
  useEffect(() => {
    const joinGroups = async () => {
      if (userRole === UserRole.TEAM_LEADER && statusMonitoringData) {
        try {
          const { signalRService } = await import("@/services/signalRService");

          // Join shift instant group
          if (statusMonitoringData.shift_instant_id) {
            await signalRService.joinShiftInstantGroup(
              statusMonitoringData.shift_instant_id
            );
          }

          // Join team group using user ID
          if (user?.id) {
            await signalRService.joinTeamGroup(user.id.toString());
          }
        } catch (error) {
          console.error("Failed to join SignalR groups:", error);
        }
      }
    };

    joinGroups();
  }, [userRole, statusMonitoringData, user?.id]);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isVisualCheckActive && visualCheckTimer > 0) {
      interval = setInterval(() => {
        setVisualCheckTimer(visualCheckTimer - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isVisualCheckActive, visualCheckTimer, setVisualCheckTimer]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isMyBackupBasketActive && myBackupBasketTimer > 0) {
      interval = setInterval(() => {
        setMyBackupBasketTimer(myBackupBasketTimer - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isMyBackupBasketActive, myBackupBasketTimer, setMyBackupBasketTimer]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isDepartmentBackupBasketActive && departmentBackupBasketTimer > 0) {
      interval = setInterval(() => {
        setDepartmentBackupBasketTimer(departmentBackupBasketTimer - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [
    isDepartmentBackupBasketActive,
    departmentBackupBasketTimer,
    setDepartmentBackupBasketTimer,
  ]);

  // Start appropriate timer if we're already on a backup basket tab when component mounts
  useEffect(() => {
    if (activeTab === "my-backup-basket" && !isMyBackupBasketActive) {
      startMyBackupBasket();
    } else if (
      activeTab === "department-backup-basket" &&
      !isDepartmentBackupBasketActive
    ) {
      startDepartmentBackupBasket();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]); // Only depend on activeTab to avoid infinite loops

  const formatTimer = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const canStartVisualCheck = () => {
    const now = new Date();
    const [shiftHour, shiftMinute] = shiftStartTime.split(":").map(Number);
    const shiftStart = new Date();
    shiftStart.setHours(shiftHour, shiftMinute, 0, 0);
    return now >= shiftStart;
  };

  // Helper functions for shift status management
  const getShiftStatus = () => {
    return statusMonitoringData?.shift_status || "INIT";
  };

  const getShiftTimes = () => {
    return {
      shiftStart: statusMonitoringData?.shift_start || "03:00",
      shiftEnd: statusMonitoringData?.shift_end || "14:00",
    };
  };

  // Helper functions for time calculations with UTC support
  const formatTimeRemaining = (targetTime: Date) => {
    const now = currentTime;
    const diff = targetTime.getTime() - now.getTime();

    if (diff <= 0) return "00:00:00";

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  };

  // Convert UTC time string to local Date object
  const parseUTCTime = (timeString: string) => {
    // Handle both "HH:MM" format and full ISO string
    if (timeString.includes("T")) {
      return new Date(timeString);
    } else {
      // For "HH:MM" format, create today's date in UTC then convert to local
      const [hours, minutes] = timeString.split(":").map(Number);
      const utcDate = new Date();
      utcDate.setUTCHours(hours, minutes, 0, 0);
      return utcDate;
    }
  };

  // Get current time in UTC for server comparison
  const getCurrentUTCTime = () => {
    return new Date();
  };

  // Format time for display (local time)
  const formatDisplayTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  };

  const getShiftTimingInfo = () => {
    const { shiftStart, shiftEnd } = getShiftTimes();
    const shiftStatus = getShiftStatus();
    const now = currentTime;

    // Parse shift times - assuming server provides UTC times, convert to local
    const shiftStartTime = parseUTCTime(shiftStart);
    const shiftEndTime = parseUTCTime(shiftEnd);

    // Display times in local timezone
    const localStartTime = formatDisplayTime(shiftStartTime);
    const localEndTime = formatDisplayTime(shiftEndTime);

    switch (shiftStatus) {
      case "INIT":
        if (now < shiftStartTime) {
          return {
            title: "Shift starts in",
            timeRemaining: formatTimeRemaining(shiftStartTime),
            subtitle: `Shift: ${localStartTime} - ${localEndTime} (Local Time)`,
            color: "text-blue-600",
            bgColor: "bg-blue-50",
            canStart: false,
          };
        } else {
          return {
            title: "Shift ready to start",
            timeRemaining: "Ready Now",
            subtitle: `Shift: ${localStartTime} - ${localEndTime} (Local Time)`,
            color: "text-green-600",
            bgColor: "bg-green-50",
            canStart: true,
          };
        }
      case "VISUAL_CHECK":
        return {
          title: "Visual Check Active",
          timeRemaining: "In Progress",
          subtitle: "Mark attendance for all employees",
          color: "text-green-600",
          bgColor: "bg-green-50",
          canStart: false,
        };
      case "STARTED":
        if (now < shiftEndTime) {
          return {
            title: "Shift ends in",
            timeRemaining: formatTimeRemaining(shiftEndTime),
            subtitle: `Shift: ${localStartTime} - ${localEndTime} (Local Time)`,
            color: "text-green-600",
            bgColor: "bg-green-50",
            canStart: false,
          };
        } else {
          return {
            title: "Shift time completed",
            timeRemaining: "Overtime",
            subtitle: "Ready to close shift",
            color: "text-red-600",
            bgColor: "bg-red-50",
            canStart: false,
          };
        }
      case "CLOSED":
        return {
          title: "Shift Closed",
          timeRemaining: "Completed",
          subtitle: "Awaiting review",
          color: "text-gray-600",
          bgColor: "bg-gray-50",
          canStart: false,
        };
      case "REVIEW":
        return {
          title: "Under Review",
          timeRemaining: "In Review",
          subtitle: "Shift data being reviewed",
          color: "text-yellow-600",
          bgColor: "bg-yellow-50",
          canStart: false,
        };
      case "VALIDATED":
        return {
          title: "Shift Validated",
          timeRemaining: "Completed",
          subtitle: "Shift completed successfully",
          color: "text-green-600",
          bgColor: "bg-green-50",
          canStart: false,
        };
      default:
        return {
          title: "Unknown Status",
          timeRemaining: "--:--:--",
          subtitle: "",
          color: "text-gray-600",
          bgColor: "bg-gray-50",
          canStart: false,
        };
    }
  };

  const getButtonState = () => {
    const shiftStatus = getShiftStatus();
    const { shiftStart } = getShiftTimes();
    const now = new Date();

    // Parse shift start time
    const [shiftHour, shiftMinute] = shiftStart.split(":").map(Number);
    const shiftStartTime = new Date();
    shiftStartTime.setHours(shiftHour, shiftMinute, 0, 0);

    const timeReached = now >= shiftStartTime;

    switch (shiftStatus) {
      case "INIT":
        return {
          disabled: !timeReached,
          text: "Start Visual Check",
          action: "visual_check",
          variant: "primary" as const,
        };
      case "VISUAL_CHECK":
        return {
          disabled: false,
          text: "Start Shift",
          action: "start_shift",
          variant: "success" as const,
        };
      case "STARTED":
        return {
          disabled: false,
          text: "Close Shift",
          action: "close_shift",
          variant: "danger" as const,
        };
      case "CLOSED":
      case "REVIEW":
      case "VALIDATED":
        return {
          disabled: true,
          text: "Shift Completed",
          action: "none",
          variant: "disabled" as const,
        };
      default:
        return {
          disabled: true,
          text: "Start Visual Check",
          action: "visual_check",
          variant: "disabled" as const,
        };
    }
  };

  const handleShiftAction = async () => {
    const buttonState = getButtonState();
    const shiftInstantId = statusMonitoringData?.shift_instant_id;

    if (!shiftInstantId) {
      toast({
        title: "Error",
        description: "Shift instant ID not found",
        variant: "destructive",
      });
      return;
    }

    try {
      switch (buttonState.action) {
        case "visual_check":
          await updateShiftStatus(shiftInstantId, "VISUAL_CHECK");
          startVisualCheck();
          break;
        case "start_shift":
          await updateShiftStatus(shiftInstantId, "STARTED");
          setIsShiftStarted(true);
          break;
        case "close_shift":
          await updateShiftStatus(shiftInstantId, "CLOSED");
          setIsEvaluationDialogOpen(true);
          break;
        default:
          break;
      }
    } catch (error: any) {
      // Handle specific UTC time error
      if (error?.response?.data?.message?.includes("before shift start time")) {
        const message = error.response.data.message;

        // Extract UTC times from error message
        const utcTimeMatch = message.match(/Shift starts at ([\d-T:Z\s]+) UTC/);
        const currentTimeMatch = message.match(
          /current time is ([\d-T:Z\s]+) UTC/
        );

        if (utcTimeMatch && currentTimeMatch) {
          const shiftStartUTC = new Date(utcTimeMatch[1]);
          const currentUTC = new Date(currentTimeMatch[1]);

          const localShiftStart = formatDisplayTime(shiftStartUTC);
          const localCurrentTime = formatDisplayTime(currentUTC);
          const timeRemaining = formatTimeRemaining(shiftStartUTC);

          toast({
            title: "Shift Not Ready",
            description: `Shift starts at ${localShiftStart} (local time). Current time: ${localCurrentTime}. Time remaining: ${timeRemaining}`,
            variant: "destructive",
          });
        } else {
          toast({
            title: "Shift Not Ready",
            description: message,
            variant: "destructive",
          });
        }
      } else {
        // Handle other errors
        const errorMessage =
          error?.response?.data?.message ||
          error?.message ||
          "An error occurred";
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    }
  };

  const handleApprove = async () => {
    try {

      const shiftInstantId =
        statusMonitoringData?.shift_instant_id ||
        useTeamLeaderStore.getState().statusMonitoringData?.shift_instant_id;

      if (!shiftInstantId) {
        throw new Error("No active shift found");
      }

      const employeeType =
        activeTab === "my-teamleaders" ? "teamleader" : "operator";

      const shiftType =
        activeTab === "my-teamleaders"
          ? useTeamLeaderStore.getState().teamLeaderShiftType || "NORMAL"
          : useTeamLeaderStore.getState().operatorShiftType || "NORMAL";

      try {
        await api.patch(
          `visual-check/api/v1/shiftleader/shift_instant/${shiftInstantId}/employee/${employeeType}/shift/${shiftType}/approve`,
          {}
        );
        
      } catch (e) {
        console.error("Failed to update team leader status:", e);
      } finally {
        approveAttendanceSheet();
        toast({
          title: "Success",
          description: "Attendance sheet approved successfully",
          variant: "default",
        });
      }
    } catch (error) {
      console.error("Error approving attendance sheet:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to approve attendance sheet",
        variant: "destructive",
      });
    } finally {
      setIsValidationDialogOpen(false);
    }
  };

  const handleOpenSendForReviewDialog = () => {
    setIsSendForReviewDialogOpen(true);
  };

  const handleCloseSendForReviewDialog = () => {
    setIsSendForReviewDialogOpen(false);
  };

  const handleSendForReview = (comment: string) => {
    // Handle send for review logic
    console.log("Sending for review with comment:", comment);
    setIsSendForReviewDialogOpen(false);
  };

  // Bulk Request Handlers
  const handleOpenBulkRequestDialog = () => {
    setIsBulkRequestDialogOpen(true);
  };

  const handleCloseBulkRequestDialog = () => {
    setIsBulkRequestDialogOpen(false);
    setSelectedBulkRequestType(undefined);
    setSelectedBulkOperators([]);
  };

  const handleBulkRequestNext = (
    requestType: { id: string; title: string },
    operators: string[]
  ) => {
    setSelectedBulkRequestType(requestType);
    setSelectedBulkOperators(operators);
    setIsBulkRequestDialogOpen(false);
    setIsBulkRequestFormDialogOpen(true);
  };

  const handleCloseBulkRequestFormDialog = () => {
    setIsBulkRequestFormDialogOpen(false);
    setSelectedBulkRequestType(undefined);
    setSelectedBulkOperators([]);
  };

  const handleBackToBulkRequestList = () => {
    setIsBulkRequestFormDialogOpen(false);
    setIsBulkRequestDialogOpen(true);
  };

  const handleTabChange = (newTab: string | ((prev: string) => string)) => {
    const tabValue = typeof newTab === "function" ? newTab(activeTab) : newTab;
    setActiveTab(tabValue);

    if (tabValue === "my-teamleaders") {
      console.log("teamLeadersShiftTypes", teamLeadersShiftTypes);
      setSelectedTeamLeaderShiftType(teamLeadersShiftTypes[0]);
    }

    if (tabValue === "my-operators") {
      console.log("operatorsShiftTypes", operatorsShiftTypes);
      setOperatorshiftType(operatorsShiftTypes[0]);
    }

    // Start timer for my-backup-basket tab
    if (tabValue === "my-backup-basket") {
      startMyBackupBasket();
    }

    // Start timer for department-backup-basket tab
    if (tabValue === "department-backup-basket") {
      startDepartmentBackupBasket();
      setDepartmentRefreshKey((prev) => prev + 1);
    }

    // Update clocking category for Department Clerk and Department Manager
    if (
      userRole === UserRole.DEPARTEMENT_CLERK ||
      userRole === UserRole.DEPARTEMENT_MANAGER
    ) {
      if (tabValue === "clocking-ih") {
        setClockingCategory("IH");
      } else if (tabValue === "clocking-is") {
        setClockingCategory("IS");
      }
    }
  };

  const handleSubmitAction = async () => {
    // Handle Shift Leader bulk actions
    if (userRole === UserRole.SHIFT_LEADER && activeTab === "my-operators") {
      const workerIds = Array.from(selectedWorkers);
      const shiftInstantId = statusMonitoringData?.shift_instant_id;

      if (shiftInstantId && (selectedAction === "P" || selectedAction === "AB")) {
        try {
          // Use the shift leader bulk update API
          await api.post(
            `visual-check/api/v1/shiftleader/shift_instant/${shiftInstantId}/operators/status/bulk`,
            {
              statusCode: selectedAction,
              operatorIds: workerIds,
              shiftType: (operatorshiftType || "NORMAL").toUpperCase(),
            }
          );

          // Refresh the data after successful update
          const endDate = currentDate.toISOString().split("T")[0];
          await fetchShiftLeaderStatusMonitoring(endDate);

          toast({
            title: "Success",
            description: `Bulk operator status updated to ${selectedAction === "P" ? "Present" : "Absent"} successfully`,
          });
        } catch (error) {
          console.error("Failed to update bulk operator status:", error);
          toast({
            title: "Error",
            description: "Failed to update bulk operator status",
            variant: "destructive",
          });
        }
      }

      setSelectedWorkers(new Set());
      setSelectedAction("");
      return;
    }

    // Handle Department Clerk bulk actions
    if (userRole === UserRole.DEPARTEMENT_CLERK && (selectedAction === "P" || selectedAction === "AB")) {
      const subordinateIds = Array.from(selectedWorkers);

      if (clerkShiftTypes.length > 0 && subordinateIds.length > 0) {
        // Get the clerk instant ID from the first shift type (assuming single clerk instant)
        const clerkInstantId = clerkShiftTypes[0]?.clerk_instant_id;

        if (clerkInstantId) {
          try {
            await updateClerkSubordinateStatusBulk(clerkInstantId, selectedAction, subordinateIds);

            toast({
              title: "Success",
              description: `Bulk subordinate status updated to ${selectedAction === "P" ? "Present" : "Absent"} successfully`,
            });
          } catch (error) {
            console.error("Failed to update bulk subordinate status:", error);
            toast({
              title: "Error",
              description: "Failed to update bulk subordinate status",
              variant: "destructive",
            });
          }
        }
      }

      setSelectedWorkers(new Set());
      setSelectedAction("");
      return;
    }

    // Handle Team Leader bulk actions (existing logic)
    if (selectedAction === "escalate" && selectedWorkers.size > 0) {
      try {
        // Clear any previous errors
        clearSendBackupError();

        // Convert selectedWorkers Set to Array for the API call
        const selectedWorkerIds = Array.from(selectedWorkers);

        // Static values for demo - in real app these would come from context/props
        const teamLeaderId = "3e2bac24-5866-4065-8a7b-914c2e077cf1";
        const shiftId = "b4bedff2-165e-4156-969f-d3b3cd025970";

        await sendBackupToShiftLeader(selectedWorkerIds, shiftId, teamLeaderId);

        // Mark that operators have been sent
        setHasOperatorsSent(true);

        // Clear selections on success
        setSelectedWorkers(new Set());
        setSelectedAction("");
      } catch (error) {
        console.error("Error sending backup to shift leader:", error);
      }
    } else if (selectedAction === "present") {
      console.log("Marking workers as present:", Array.from(selectedWorkers));

      const workerIds = Array.from(selectedWorkers);
      const shiftInstantId = statusMonitoringData?.shift_instant_id;

      if (shiftInstantId) {
        await updateOperatorAttendanceStatusBulk(
          shiftInstantId,
          "P",
          workerIds
        );
      }
      setSelectedWorkers(new Set());
      setSelectedAction("");


    } else if (selectedAction === "absent") {
      // Handle absent action
      console.log("Marking workers as absent:", Array.from(selectedWorkers));
      setSelectedWorkers(new Set());

      const workerIds = Array.from(selectedWorkers);
      const shiftInstantId = statusMonitoringData?.shift_instant_id;
      if (shiftInstantId) {
        await updateOperatorAttendanceStatusBulk(
          shiftInstantId,
          "AB",
          workerIds
        );
      }
      setSelectedAction("");
    } else {
      setSelectedWorkers(new Set());

      const workerIds = Array.from(selectedWorkers);
      const shiftInstantId = statusMonitoringData?.shift_instant_id;
      if (shiftInstantId) {
        await updateOperatorAttendanceStatusBulk(
          shiftInstantId,
          selectedAction,
          workerIds
        );
      }
      setSelectedAction("");
    }
  };

  const shouldShowApproveButton = () => {
    return (
      userRole === UserRole.DEPARTEMENT_CLERK ||
      userRole === UserRole.SHIFT_LEADER ||
      userRole === UserRole.QUALITY_SUPERVISOR
    );
  };

  const handleShiftTypeChange = (newShiftType: string) => {
    setSelectedTeamLeaderShiftType(newShiftType);
    setTeamLeaderShiftType(newShiftType);
  };

  const handleOperatorShiftTypeChange = (newShiftType: string) => {
    setOperatorshiftType(newShiftType);
    setOperatorShiftType(newShiftType);
  };

  return (
    <div className="flex flex-col gap-4 w-full relative">
      {/* Hide tabs if special shift leader validation view is active */}
      {!selectedValidationTeam &&
        (userRole === UserRole.DEPARTEMENT_CLERK ||
        userRole === UserRole.DEPARTEMENT_MANAGER ? (
          <>
            <DepartmentClerkTabs
              counts={departmentClerkTabCounts}
              activeTab={activeTab}
              setActiveTab={handleTabChange}
            />
            <div className="w-full overflow-x-auto">
              <div className="flex items-center justify-between min-w-max">
                <div className="flex items-center gap-3 flex-shrink-0 min-w-0">
                  <div className="p-1 rounded-md flex-shrink-0">
                    <CustomIcon
                      name="dayView"
                      style={{ width: "30px", height: "30px", fill: "#5B7291" }}
                    />
                  </div>
                  <div className="min-w-0">
                    <h1 className="text-lg font-medium text-gray-700 truncate">
                      Attendance sheet :
                    </h1>
                    <p className="text-sm text-gray-500 truncate">
                      Morning shift ({shiftStartTime} AM to 14:00 AM)
                    </p>
                  </div>
                </div>
                {userRole === UserRole.DEPARTEMENT_CLERK ? (
                  <div className="flex gap-3 ml-auto">
                    <DatePicker
                      label="Date"
                      placeholder="Select a date"
                      className="min-w-[200px] mt-[-20px]"
                      defaultDate={selectedDate}
                      onChange={handleDateChange}
                    />
                    {clerkShiftTypes.length > 0 && (
                      <CustomSelect
                        options={clerkShiftTypes.map((shift) => ({
                          label: `${shift.shift_type} - ${shift.clerk_instant_id}`,
                          value: shift.clerk_instant_id,
                        }))}
                        onValueChange={setSelectedShiftType}
                        value={selectedShiftType}
                        placeholder="Select shift type"
                        label="Shift Type"
                        className="min-w-[200px] mt-[-20px]"
                        disabled={isClerkShiftTypesLoading}
                      />
                    )}
                    <Button
                      onClick={handleOpenBulkRequestDialog}
                      className="flex items-center gap-2 px-3 py-5 rounded-lg bg-black text-white font-semibold shadow hover:bg-gray-800 focus:outline-none"
                    >
                      Bulk Request
                    </Button>
                    <IconButton
                      icon={addIcon}
                      label="Approve"
                      onClick={() => setIsValidationDialogOpen(true)}
                      className="bg-black text-white rounded-lg px-3 py-5 flex items-center justify-center gap-1"
                    />
                  </div>
                ) : userRole === UserRole.DEPARTEMENT_MANAGER ? (
                  <div className="flex gap-3 ml-auto">
                    <DatePicker
                      label="Date"
                      placeholder="Select a date"
                      className="min-w-[200px] mt-[-20px]"
                      defaultDate={selectedDate}
                      onChange={handleDateChange}
                    />

                    <div>{shiftTypes.length}</div>

                    <Button
                      className="flex items-center gap-2 px-3 py-5 rounded-lg border-2 border-green-600 bg-black text-white font-semibold shadow hover:bg-green-900 focus:outline-none"
                      style={{ borderColor: "#1DB954" }}
                      onClick={() => {}}
                    >
                      <svg
                        width="20"
                        height="20"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        className="text-green-400"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      Confirm
                    </Button>
                    <Button
                      className="flex items-center gap-2 px-3 py-5 rounded-lg border-2 border-red-600 bg-black text-white font-semibold shadow hover:bg-red-900 focus:outline-none"
                      style={{ borderColor: "#FF3B30" }}
                      onClick={handleOpenSendForReviewDialog}
                    >
                      <svg
                        width="20"
                        height="20"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        className="text-red-400"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>
                      Send for review
                    </Button>
                  </div>
                ) : null}
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="w-full overflow-x-auto">
              <div className="flex flex-col gap-4 min-w-max">
                {/* First row - Filters and action buttons */}
                <div className="flex items-center gap-2">
                  {userRole === UserRole.SHIFT_LEADER && tabCounts ? (
                    <>
                      <ShiftLeaderTabs
                        counts={tabCounts}
                        activeTab={activeTab}
                        setActiveTab={handleTabChange}
                      />

                      {/* Date filter for Shift Leader */}
                      <div className="flex-1 min-w-[200px]">
                        <DatePicker
                          label="Date"
                          placeholder="Select a date"
                          defaultDate={currentDate}
                          onChange={(date) => date && setCurrentDate(date)}
                        />
                      </div>

                      {/* {activeTab === "my-teamleaders" &&
                        teamLeadersShiftTypes.length > 0 && (
                          <div>
                            <select
                              name="shiftType"
                              value={selectedTeamLeaderShiftType}
                              onChange={(e) =>
                                handleShiftTypeChange(e.target.value)
                              }
                            >
                              {teamLeadersShiftTypes.map((shiftType) => (
                                <option key={shiftType} value={shiftType}>
                                  {shiftType}
                                </option>
                              ))}
                            </select>
                          </div>
                        )} */}
                      {activeTab === "my-operators" &&

                        operatorsShiftTypes.length > 0 && (


                          <div className="flex ">
                                                  <div>Shift Type :</div>

                            <select
                              name="shiftType"
                              value={operatorshiftType}
                              onChange={(e) =>
                                handleOperatorShiftTypeChange(e.target.value)
                              }
                            >
                              {operatorsShiftTypes.map((shiftType) => (
                                <option key={shiftType} value={shiftType}>
                                  {shiftType}
                                </option>
                              ))}
                            </select>
                          </div>
                        )}
                      {(activeTab === "my-backup-basket" ||
                        activeTab === "department-backup-basket") && (
                        <>
                          <div className="flex items-center gap-2 px-3 py-2 rounded-md flex-shrink-0">
                            <CustomIcon
                              name="stopWatch"
                              style={{
                                width: "32px",
                                height: "36px",
                                fill: "#F84018",
                              }}
                            />
                            <span className="text-xl font-bold truncate text-[#F84018]">
                              {isMyBackupBasketActive
                                ? formatTimer(myBackupBasketTimer)
                                : isDepartmentBackupBasketActive
                                  ? formatTimer(departmentBackupBasketTimer)
                                  : "00:00"}
                            </span>
                          </div>
                        </>
                      )}
                    </>
                  ) : userRole === UserRole.QUALITY_SUPERVISOR ? (
                    // Filters for Quality Supervisor
                    <div className="bg-[#F5FBFF] border border-[#87B0E6] rounded-lg p-2 flex-grow">
                      <div className="flex items-center gap-2 flex-nowrap h-full">
                        <div className="flex-1">
                          <DatePicker
                            label="Date"
                            placeholder="Select a date"
                            defaultDate={currentDate}
                            onChange={(date) => date && setCurrentDate(date)}
                          />
                        </div>
                        <div className="flex-1">
                          <CustomSelect
                            options={[]}
                            onValueChange={(value) => console.log(value)}
                            placeholder="Select area"
                            label={
                              <span className="flex items-center">
                                {AreaIcons}
                                Area
                              </span>
                            }
                          />
                        </div>
                        <div className="flex-1">
                          <CustomSelect
                            options={[]}
                            onValueChange={(value) => console.log(value)}
                            placeholder="Select Team"
                            label={
                              <span className="flex items-center">
                                {TeamIcons}
                                Team
                              </span>
                            }
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    // Original filters for Team Leader and other roles
                    <div className="bg-[#F5FBFF] border border-[#87B0E6] rounded-lg p-2 flex-grow">
                      <div className="flex items-center gap-2 flex-nowrap h-full">
                        <div className="flex-1 min-w-[200px]">
                          <DatePicker
                            label="Date"
                            placeholder="Select a date"
                            defaultDate={currentDate}
                            onChange={(date) => date && handleDateChange(date)}
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.customerOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              })
                            )}
                            onValueChange={setSelectedCustomer}
                            defaultValue={teamLeaderFilters.selectedCustomer}
                            placeholder="Select customer"
                            label={
                              <span className="flex items-center">
                                {CustomerIcons}
                                Customer
                              </span>
                            }
                            disabled={teamLeaderFilters.isLoading}
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.projectOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              })
                            )}
                            onValueChange={setSelectedProject}
                            defaultValue={teamLeaderFilters.selectedProject}
                            placeholder="Select project"
                            label={
                              <span className="flex items-center">
                                {ProjectIcons}
                                Project
                              </span>
                            }
                            disabled={
                              !teamLeaderFilters.selectedCustomer ||
                              teamLeaderFilters.isLoading ||
                              teamLeaderFilters.projectOptions.length === 0
                            }
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.familyOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              })
                            )}
                            onValueChange={setSelectedFamily}
                            defaultValue={teamLeaderFilters.selectedFamily}
                            placeholder="Select family"
                            label={
                              <span className="flex items-center">
                                {FamilyIcons}
                                Family
                              </span>
                            }
                            disabled={
                              !teamLeaderFilters.selectedProject ||
                              teamLeaderFilters.isLoading ||
                              teamLeaderFilters.familyOptions.length === 0
                            }
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.valueStreamOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              })
                            )}
                            onValueChange={setSelectedValueStream}
                            defaultValue={teamLeaderFilters.selectedValueStream}
                            placeholder="Select value stream"
                            label={
                              <span className="flex items-center">
                                {LineChartIcons}
                                Value Stream
                              </span>
                            }
                            disabled={
                              !teamLeaderFilters.selectedFamily ||
                              teamLeaderFilters.isLoading ||
                              teamLeaderFilters.valueStreamOptions.length === 0
                            }
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.areaOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              })
                            )}
                            onValueChange={setSelectedArea}
                            defaultValue={teamLeaderFilters.selectedArea}
                            placeholder="Select area"
                            label={
                              <span className="flex items-center">
                                {AreaIcons}
                                Area
                              </span>
                            }
                            disabled={
                              !teamLeaderFilters.selectedValueStream ||
                              teamLeaderFilters.isLoading ||
                              teamLeaderFilters.areaOptions.length === 0
                            }
                          />
                        </div>
                        <div className="flex-1 min-w-[200px]">
                          <CustomSelect
                            options={teamLeaderFilters.teamOptions.map(
                              (option) => ({
                                label: option.value,
                                value: option.id,
                              })
                            )}
                            onValueChange={setSelectedTeam}
                            defaultValue={teamLeaderFilters.selectedTeam}
                            placeholder="Select Team"
                            label={
                              <span className="flex items-center">
                                {TeamIcons}
                                Team
                              </span>
                            }
                            disabled={
                              !teamLeaderFilters.selectedArea ||
                              teamLeaderFilters.isLoading ||
                              teamLeaderFilters.teamOptions.length === 0
                            }
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  {/* Shift Timing Display for Team Leader */}
                  {/* {userRole === UserRole.TEAM_LEADER && statusMonitoringData && (
                    <div className="flex-shrink-0 mr-4">
                      {(() => {
                        const timingInfo = getShiftTimingInfo();
                        return (
                          <div className={`px-4 py-2 rounded-lg border ${timingInfo.bgColor} ${timingInfo.color} border-gray-200`}>
                            <div className="text-sm font-semibold">{timingInfo.title}</div>
                            <div className="text-lg font-mono font-bold">{timingInfo.timeRemaining}</div>
                            <div className="text-xs opacity-75">{timingInfo.subtitle}</div>
                          </div>
                        );
                      })()}
                    </div>
                  )} */}

                  <div className="flex-shrink-0 ml-auto">
                    {userRole !== UserRole.SHIFT_LEADER &&
                      (() => {
                        if (
                          userRole === UserRole.TEAM_LEADER &&
                          statusMonitoringData
                        ) {
                          const buttonState = getButtonState();
                          const getButtonStyle = () => {
                            switch (buttonState.variant) {
                              case "primary":
                                return "bg-[#4762F1] border-2 border-[#005A04] text-white hover:bg-[#3a56c5]";
                              case "success":
                                return "bg-[#4762F1] border-2 border-[#005A04] text-white hover:bg-[#3a56c5]";
                              case "danger":
                                return "bg-black border-2 border-red-600 text-[#F84018]";
                              case "disabled":
                                return "bg-gray-400 border-gray-300 text-gray-200";
                              default:
                                return "bg-gray-400 border-gray-300 text-gray-200";
                            }
                          };

                          return (
                            <Button
                              onClick={handleShiftAction}
                              disabled={buttonState.disabled}
                              className={`flex items-center font-bold rounded-lg shadow-md disabled:bg-gray-400 disabled:border-gray-300 disabled:text-gray-200 ${getButtonStyle()}`}
                              style={{
                                minWidth: "100px",
                                minHeight: "56px",
                                marginRight: "5px",
                              }}
                            >
                              {PlayIcons}
                              <span className="flex flex-col items-start text-left text-white leading-tight">
                                {buttonState.text.includes("Visual") ? (
                                  <>
                                    Start Visual
                                    <br />
                                    Check
                                  </>
                                ) : (
                                  buttonState.text
                                )}
                              </span>
                            </Button>
                          );
                        }

                        return isVisualCheckActive ? (
                          isShiftStarted ? (
                            <Button
                              onClick={() => {
                                setIsEvaluationDialogOpen(true);
                              }}
                              className="flex items-center bg-black border-2 border-red-600 text-[#F84018] font-bold rounded-lg shadow-md disabled:bg-gray-400 disabled:border-gray-300 disabled:text-gray-200"
                              style={{
                                minWidth: "100px",
                                minHeight: "56px",
                                marginRight: "5px",
                              }}
                            >
                              {PlayIcons}
                              <span className="flex flex-col items-start text-left text-white leading-tight">
                                Close Shift
                              </span>
                            </Button>
                          ) : (
                            <Button
                              onClick={() => setIsShiftStarted(true)}
                              disabled={!hasOperatorsSent}
                              className="flex items-center bg-[#4762F1] border-2 border-[#005A04] text-white font-bold rounded-lg shadow-md hover:bg-[#3a56c5] disabled:bg-gray-400 disabled:border-gray-300 disabled:text-gray-200"
                              style={{
                                minWidth: "100px",
                                minHeight: "56px",
                                marginRight: "5px",
                              }}
                            >
                              {PlayIcons}
                              <span className="flex flex-col items-start text-left leading-tight">
                                Start Shift
                              </span>
                            </Button>
                          )
                        ) : (
                          <Button
                            onClick={startVisualCheck}
                            disabled={!canStartVisualCheck()}
                            className="flex items-center bg-[#4762F1] border-2 border-[#005A04] text-white font-bold rounded-lg shadow-md hover:bg-[#3a56c5] disabled:bg-gray-400 disabled:border-gray-300 disabled:text-gray-200"
                            style={{
                              minWidth: "100px",
                              minHeight: "56px",
                              marginRight: "5px",
                            }}
                          >
                            {PlayIcons}
                            <span className="flex flex-col items-start text-left leading-tight">
                              Start Visual
                              <br />
                              Check
                            </span>
                          </Button>
                        );
                      })()}
                  </div>
                </div>

                {/* Second row - Attendance sheet info and other elements */}
                <div className="flex items-center gap-4">
                  {activeTab !== "my-backup-basket" &&
                    activeTab !== "department-backup-basket" && (
                      <div className="flex items-center gap-3 flex-shrink-0 min-w-0">
                        <div className="p-1 rounded-md flex-shrink-0">
                          <CustomIcon
                            name="dayView"
                            style={{
                              width: "30px",
                              height: "30px",
                              fill: "#5B7291",
                            }}
                          />
                        </div>
                        <div className="min-w-0">
                          <h1 className="text-lg font-medium text-gray-700 truncate">
                            Attendance sheet :
                          </h1>
                          {userRole === UserRole.TEAM_LEADER &&
                          statusMonitoringData ? (
                            (() => {
                              const timingInfo = getShiftTimingInfo();
                              return (
                                <div className="flex flex-col">
                                  <p className="text-sm text-gray-500 truncate">
                                    {timingInfo.subtitle}
                                  </p>
                                  <div
                                    className={`text-xs font-medium ${timingInfo.color} flex items-center gap-2`}
                                  >
                                    <span>{timingInfo.title}:</span>
                                    <span className="font-mono font-bold">
                                      {timingInfo.timeRemaining}
                                    </span>
                                    {timingInfo.canStart && (
                                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Ready
                                      </span>
                                    )}
                                  </div>
                                </div>
                              );
                            })()
                          ) : (
                            <p className="text-sm text-gray-500 truncate">
                              Morning shift({shiftStartTime} AM to 14:00 AM)
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                  {userRole !== UserRole.SHIFT_LEADER &&
                    userRole !== UserRole.QUALITY_SUPERVISOR && (
                      <>
                        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />

                        {!isVisualCheckActive ? (
                          <>
                            <div className="flex items-center gap-2 bg-orange-50 px-3 py-2 rounded-md flex-shrink-0">
                              <CustomIcon
                                name="stopWatch"
                                style={{
                                  width: "32px",
                                  height: "36px",
                                  fill: "#F84018",
                                }}
                              />
                              <span className="text-xl font-bold truncate text-[#F84018]">
                                05:00
                              </span>
                            </div>
                          </>
                        ) : (
                          <div className="flex items-center gap-2 bg-orange-50 px-3 py-2 rounded-md flex-shrink-0">
                            <CustomIcon
                              name="stopWatch"
                              style={{
                                width: "32px",
                                height: "36px",
                                fill: "#F84018",
                              }}
                            />
                            <span className="text-xl font-bold truncate text-[#F84018]">
                              {formatTimer(visualCheckTimer)}
                            </span>
                          </div>
                        )}
                      </>
                    )}
                  {/* Bulk actions for Shift Leader on my-operators tab */}
                  {userRole === UserRole.SHIFT_LEADER && activeTab === "my-operators" && selectedWorkers.size > 0 && (
                    <>
                      <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                      <div
                        className="flex flex-wrap items-center gap-3 md:gap-4 lg:gap-6 w-full max-w-full"
                        style={{ minWidth: 0 }}
                      >
                        <div className="flex items-center gap-2 mb-2 md:mb-0">
                          <span className="text-sm font-medium text-gray-700">
                            Selected: {selectedWorkers.size}
                          </span>
                          <select
                            value={selectedAction}
                            className="p-2 border rounded"
                            onChange={(e) => setSelectedAction(e.target.value)}
                          >
                            <option value="">Select Action</option>
                            <option value="P">Present</option>
                            <option value="AB">Absent</option>
                          </select>
                        </div>
                        <div className="flex flex-row flex-wrap gap-2 md:gap-3 mb-2 md:mb-0">
                          <ReusableButton
                            label="Submit"
                            onClick={handleSubmitAction}
                            disabled={!selectedAction}
                            className="bg-black text-white rounded-lg px-3 py-3 md:px-4 md:py-4 flex items-center justify-center gap-1 min-w-[90px] disabled:bg-gray-400 disabled:cursor-not-allowed"
                          />
                          <ReusableButton
                            label="Cancel"
                            onClick={() => {
                              setSelectedWorkers(new Set());
                              setSelectedAction("");
                            }}
                            className="bg-white text-black hover:bg-gray-100 rounded-lg px-3 py-3 md:px-4 md:py-4 flex items-center justify-center gap-1 min-w-[90px] border border-gray-300"
                          />
                        </div>
                      </div>
                    </>
                  )}
                  {/* Bulk actions for Department Clerk */}
                  {userRole === UserRole.DEPARTEMENT_CLERK && selectedWorkers?.size > 0 && (
                    <>
                      <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                      <div
                        className="flex flex-wrap items-center gap-3 md:gap-4 lg:gap-6 w-full max-w-full"
                        style={{ minWidth: 0 }}
                      >
                        <div className="flex items-center gap-2 mb-2 md:mb-0">
                          <span className="text-sm font-medium text-gray-700">
                            Selected: {selectedWorkers.size}
                          </span>
                          <select
                            value={selectedAction}
                            className="p-2 border rounded"
                            onChange={(e) => setSelectedAction(e.target.value)}
                          >
                            <option value="">Select Action</option>
                            <option value="P">Present</option>
                            <option value="AB">Absent</option>
                          </select>
                        </div>
                        <div className="flex flex-row flex-wrap gap-2 md:gap-3 mb-2 md:mb-0">
                          <ReusableButton
                            label="Submit"
                            onClick={handleSubmitAction}
                            disabled={!selectedAction}
                            className="bg-black text-white rounded-lg px-3 py-3 md:px-4 md:py-4 flex items-center justify-center gap-1 min-w-[90px] disabled:bg-gray-400 disabled:cursor-not-allowed"
                          />
                          <ReusableButton
                            label="Cancel"
                            onClick={() => {
                              setSelectedWorkers(new Set());
                              setSelectedAction("");
                            }}
                            className="bg-white text-black hover:bg-gray-100 rounded-lg px-3 py-3 md:px-4 md:py-4 flex items-center justify-center gap-1 min-w-[90px] border border-gray-300"
                          />
                        </div>
                      </div>
                    </>
                  )}
                  {userRole === UserRole.TEAM_LEADER && isVisualCheckActive && (
                    <>
                      <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                      <div
                        className="flex flex-wrap items-center gap-3 md:gap-4 lg:gap-6 w-full max-w-full"
                        style={{ minWidth: 0 }}
                      >
                        {selectedWorkers.size > 0 && (
                          <>
                            <div className="flex-shrink-0 min-w-[180px] mb-2 md:mb-0">
                              {/* <CustomSelect
                                options={[
                                  {
                                    label: "Present",
                                    value: "present",
                                  },
                                  {
                                    label: "Absent",
                                    value: "absent",
                                  },
                                  ...(filterMfgOnly
                                    ? [
                                        {
                                          label: "Send To ShiftLeader",
                                          value: "escalate",
                                        },
                                      ]
                                    : []),
                                ]}
                                onValueChange={(value) =>
                                  setSelectedAction(value)
                                }
                                value={selectedAction}
                                placeholder="Select Action"
                              /> */}

                              {/* onChange={(e) => handleStatusSelect(e.target.value)} */}

                              <select
                                value={selectedAction}
                                className="p-2 border rounded"
                                   onChange={(e) =>
                                    setSelectedAction(e.target.value)
                                  }
                              >
                                <option value="">Select Action</option>
                                {getStatusOptions().map((option) => (
                                  <option
                                    key={option.value}
                                    value={option.value}
                                    
                                  
                                  >
                                    {option.label}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div className="flex flex-row flex-wrap gap-2 md:gap-3 mb-2 md:mb-0">
                              <ReusableButton
                                label={
                                  isSendingBackup ? "Submitting..." : "Submit"
                                }
                                onClick={handleSubmitAction}
                                disabled={!selectedAction}
                                className="bg-black text-white rounded-lg px-3 py-3 md:px-4 md:py-4 flex items-center justify-center gap-1 min-w-[90px] disabled:bg-gray-400 disabled:cursor-not-allowed"
                              />
                              <ReusableButton
                                label="Cancel"
                                onClick={() => {
                                  setSelectedWorkers(new Set());
                                  setSelectedAction("");
                                }}
                                className="bg-white text-black hover:bg-gray-100 rounded-lg px-3 py-3 md:px-4 md:py-4 flex items-center justify-center gap-1 min-w-[90px] border border-gray-300"
                              />
                            </div>
                            <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                          </>
                        )}

                        <div className="flex items-center gap-2 ml-0 md:ml-4">
                          <Switch
                            id="mfg-filter"
                            checked={filterMfgOnly}
                            onCheckedChange={toggleMfgFilter}
                          />
                          <Label htmlFor="mfg-filter" className="text-sm">
                            MFG Structure (Only)
                          </Label>
                        </div>
                        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                        <div className="flex items-center b">
                          <Label htmlFor="mfg-filter" className="text-sm">
                            All Backup ({filteredWorkers.length})
                          </Label>
                        </div>
                        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                        <div className="flex items-center">
                          <Label htmlFor="mfg-filter" className="text-sm">
                            Used Backup ({filteredWorkers.length})
                          </Label>
                        </div>
                        <div className="hidden sm:block w-px h-8 bg-gray-300 opacity-60 flex-shrink-0" />
                        <div className="flex items-center  pr-3">
                          <Label htmlFor="mfg-filter" className="text-sm">
                            Unused Backup ({filteredWorkers.length})
                          </Label>
                        </div>
                      </div>
                    </>
                  )}
                  {/* Bulk Request Button for Team Leader */}
                  {userRole === UserRole.TEAM_LEADER && (
                    <div className="flex items-center gap-3 ml-auto">
                      {shiftTypes.length > 0 && (




                            <div className="flex gap-2">
                                <span>
                                  Shift Type:
                                </span>

                                <select
                                  name="shiftType"
                                  value={selectedShiftType}
                                  onChange={(e) =>
                                    setSelectedShiftType(e.target.value)
                                  }
                                >
                                  {shiftTypes.map((shiftType) => (
                                    <option key={shiftType.shiftInstantId} value={shiftType.shiftInstantId}>
                                      {shiftType.designation}
                                    </option>
                                  ))}
                                </select>
                            </div>



      
                    
                      
                      )}
                      <Button
                        onClick={handleOpenBulkRequestDialog}
                        className="flex items-center gap-2 px-3 py-5 rounded-lg bg-black text-white font-semibold shadow hover:bg-gray-800 focus:outline-none"
                      >
                        Bulk Request
                      </Button>
                    </div>
                  )}
                  {shouldShowApproveButton() &&
                    activeTab !== "my-backup-basket" &&
                    activeTab !== "department-backup-basket"  && (
                      <div className="flex gap-3 ml-auto">

                        {
                          activeTab == 'my-operators' 
                          && <>
                          
                           <Button
                          
                          onClick={handleOpenBulkRequestDialog}
                          className="flex items-center gap-2 px-3 py-5 rounded-lg bg-black text-white font-semibold shadow hover:bg-gray-800 focus:outline-none"
                        >
                          Bulk Request
                        </Button></>
                        }
                       
                        <IconButton
                          icon={addIcon}
                          label="Approve"
                          onClick={() => setIsValidationDialogOpen(true)}
                          className="bg-black text-white rounded-lg px-3 py-5 flex items-center justify-center gap-1"
                        />
                      </div>
                    )}
                </div>
              </div>
            </div>
          </>
        ))}
      <ValidationDialog
        isDialogOpen={isValidationDialogOpen}
        setIsDialogOpen={setIsValidationDialogOpen}
        handleConfirm={handleApprove}
        labelCancel="Cancel"
        labelConfirm="Approve"
        isLoading={false}
      >
        Would you like to approve the presence sheet?
      </ValidationDialog>
      <SendForReviewDialog
        isOpen={isSendForReviewDialogOpen}
        onClose={handleCloseSendForReviewDialog}
        onSubmit={handleSendForReview}
        teamLeaderId="4857"
        teamLeaderName="Amine SIDKI"
        teamName="TEAM 1"
        shiftTime="Morning shift (06:00 AM to 14:00 AM)"
      />
      {activeTab !== "my-backup-basket" &&
        activeTab !== "department-backup-basket" && (
          <MyTeamTable
            selectedShiftType={shiftTypes.find((s) => s.shiftInstantId == selectedShiftType)?.shiftType}
            activeTab={activeTab}
            selectedWorkers={selectedWorkers}
            setSelectedWorkers={setSelectedWorkers}
            filteredWorkersLength={filteredWorkers.length}
          />
        )}
      {activeTab === "my-backup-basket" && <MatchingInterface />}
      {activeTab === "department-backup-basket" && (
        <DepartmentBackup refreshKey={departmentRefreshKey} />
      )}
      <ReplacementFeedbackDialog
        isOpen={isEvaluationDialogOpen}
        onClose={() => setIsEvaluationDialogOpen(false)}
      />

      {/* Bulk Request Dialogs */}
      <BulkRequestDialog
        isOpen={isBulkRequestDialogOpen}
        onClose={handleCloseBulkRequestDialog}
        onNext={handleBulkRequestNext}
      />

      <RequestFormDialog
        isOpen={isBulkRequestFormDialogOpen}
        onClose={handleCloseBulkRequestFormDialog}
        onBack={handleBackToBulkRequestList}
        requestType={selectedBulkRequestType}
        selectedOperators={selectedBulkOperators}
        isBulkRequest={true}
      />
    </div>
  );
}
