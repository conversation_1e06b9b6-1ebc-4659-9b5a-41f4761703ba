import { Dispatch, SetStateAction } from "react";
import {
  CustomTabs,
  CustomTabsList,
  CustomTabsTrigger,
} from "@/components/common/CustomTabs";

interface DepartmentClerkTabsProps {
  counts: {
    ih: number;
    is: number;
  };
  activeTab: string;
  setActiveTab: Dispatch<SetStateAction<string>>;
}

export function DepartmentClerkTabs({
  counts,
  activeTab,
  setActiveTab,
}: DepartmentClerkTabsProps) {
  return (
    <div className="w-full">
      <CustomTabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <CustomTabsList>
          <CustomTabsTrigger value="clocking-ih">
            Clocking (IH) 
          </CustomTabsTrigger>
          <CustomTabsTrigger value="clocking-is">
            Clocking (IS) 
          </CustomTabsTrigger>
        </CustomTabsList>
      </CustomTabs>
    </div>
  );
}
