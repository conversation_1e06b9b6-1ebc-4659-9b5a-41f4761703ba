// Simple mock users with just name and userID
export interface SimpleMockUser {
  id: string;
  name: string;
  role?: string; // Optional role field
  legacyId?: string;
}

export const mockUsers: SimpleMockUser[] = [
  {
    legacyId: "9799",
    role: "plant manager",
    name: "<PERSON><PERSON>IB<PERSON>, NAOUFAL",
    id: "9aeb713b-97bb-4987-a2ee-0bd6e6042c19",
  },
  {
    legacyId: "10558",
    role: "department manager",
    name: "El Attaoui, Souheil",
    id: "619505b0-972c-4b9d-8430-d80ae42a1624",
  },
  {
    legacyId: "1102",
    role: "department manager",
    name: "<PERSON><PERSON><PERSON><PERSON>, MOHAMED",
    id: "c2b83ce9-191f-42c6-8ebe-61d3edb872bb",
  },
  {
    legacyId: "4706",
    role: "department manager",
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>, JAMAL",
    id: "7ce845fc-c06c-4864-8c26-10902033b490",
  },
  {
    legacyId: "7311",
    role: "department manager",
    name: "BARKANI, GHIZLANE",
    id: "05e40c6d-2af5-4f91-8dda-0b1123d47e85",
  },
  {
    legacyId: "10723",
    role: "department manager",
    name: "EL ARABI, AHMED",
    id: "c76bc408-fd55-4c69-b236-c15368065184",
  },
  {
    legacyId: "1032",
    role: "department manager",
    name: "ZINELAABIDINE, IMANE",
    id: "e9422578-8559-4111-ad9a-9dc94264f604",
  },
  {
    legacyId: "54",
    role: "coordinator",
    name: "KHEBAZAT, FADIL",
    id: "70d1e77a-0dce-48f3-9b88-4b4fdd39812f",
  },
  {
    legacyId: "763",
    role: "coordinator",
    name: "CHIHANI, HASSAN",
    id: "f238133c-dd5c-4fdf-829d-4dcbc53192a0",
  },
  {
    legacyId: "5091",
    role: "coordinator",
    name: "OUSRHIR, SAID",
    id: "acd44113-643f-4553-ad3e-b837d1ad9266",
  },
  {
    legacyId: "38",
    role: "coordinator",
    name: "BENNICH, MOHAMED",
    id: "f9e7b91f-17b2-4fcb-9599-901d1c29e467",
  },
  {
    legacyId: "353",
    role: "coordinator",
    name: "EL AMRI, MUSTAPHA",
    id: "11de86d2-0056-43e5-b9f6-5c570b6f1579",
  },
  {
    legacyId: "8056",
    role: "coordinator",
    name: "ATTOU, JAOUAD",
    id: "998de04f-abe8-4f58-979f-c17a36212d02",
  },
  {
    legacyId: "10109",
    role: "coordinator",
    name: "Moumni, Ayoub",
    id: "56f6eabd-e178-405f-8d28-3f0f2d450243",
  },
  {
    legacyId: "10112",
    role: "coordinator",
    name: "Krimaa, khaoula",
    id: "c5bb65ee-148f-4ce0-8501-2a9fcb315d10",
  },
  {
    legacyId: "10580",
    role: "coordinator",
    name: "Sairi, Mouad",
    id: "9bf62386-6cfe-461b-a3cc-6c37cfee3032",
  },
  {
    legacyId: "10800",
    role: "administration coordinator",
    name: "Serehali, Abdellatif",
    id: "661ab5b0-d47e-4878-9603-539937d09a62",
  },
  {
    legacyId: "30",
    role: "trainer",
    name: "HAJJAJ, FATIMA",
    id: "3ffe527d-b811-4372-a537-6c63a901cc6c",
  },
  {
    legacyId: "80",
    role: "trainer",
    name: "BARI, HAFID",
    id: "f9bb7c23-6414-4e5e-a09f-8ff04d80f605",
  },
  {
    legacyId: "112",
    role: "trainer",
    name: "HLIMI, TARIQ",
    id: "14766484-0512-4fc7-8f86-6a9319185642",
  },
  {
    legacyId: "113",
    role: "trainer",
    name: "OUGHAINE, ABDERRAHMANE",
    id: "c5ed5498-61c7-4fcf-b208-e699ab8ca060",
  },
  {
    legacyId: "10569",
    role: "training specialist",
    name: "Benaguid, Achraf",
    id: "4107bddb-511b-454b-93a1-ca6973936eee",
  },
  {
    legacyId: "10721",
    role: "me engineer",
    name: "AZKOUR, Sara",
    id: "4015492d-bcb9-42d5-bde7-71a29e1b5e29",
  },
  {
    legacyId: "10494",
    role: "me engineer",
    name: "Dirai, Wissal",
    id: "f76a2cb5-11bd-4919-b4f7-9d7a143448fd",
  },
  {
    legacyId: "897",
    role: "team leader",
    name: "DRIOUICH, ABDELMOTALIB",
    id: "130bed41-6b65-464e-8e46-ce4de82d8d7c",
  },
  {
    legacyId: "1257",
    role: "team leader",
    name: "MOUIRI, MUSTAPHA",
    id: "4d918b87-786b-4b87-bfc4-2a22853ecac3",
  },
  {
    legacyId: "743",
    role: "team leader",
    name: "EL JABIRY, MOHAMED",
    id: "35bffd1f-10d2-442d-bcc6-0e1f57421abd",
  },
  {
    legacyId: "98",
    role: "team leader",
    name: "AOUISS, JAOUAD",
    id: "97a3e592-c817-4699-9f45-7b659c3cf358",
  },
  {
    legacyId: "228",
    role: "team leader",
    name: "EL HASANY, NOURA",
    id: "38365773-172c-42ec-8135-34ac6c2012a8",
  },
  {
    legacyId: "883",
    role: "shift leader",
    name: "HIBBOU, BENAISSA",
    id: "8cf559c5-06f7-4d18-87fa-f3673cd11305",
  },
  {
    legacyId: "354",
    role: "shift leader",
    name: "OUBOUTAYEB, RACHID",
    id: "d5a11e90-9e54-4afc-b608-b94f4bc11695",
  },
  {
    legacyId: "883",
    role: "shift leader",
    name: "HIBBOU, BENAISSA",
    id: "8cf559c5-06f7-4d18-87fa-f3673cd11305",
  },
  {
    legacyId: "1296",
    role: "shift leader",
    name: "RKHAILA, HAMID",
    id: "459f4249-6f31-43bb-afdd-93bf9fbf97bf",
  },
  {
    legacyId: "87",
    role: "shift leader",
    name: "BENHALLI, ANOUAR",
    id: "e415531a-e529-466f-80e5-67dc37edd07a",
  },
  {
    legacyId: "883",
    role: "shift leader",
    name: "HIBBOU, BENAISSA",
    id: "8cf559c5-06f7-4d18-87fa-f3673cd11305",
  },
  {
    legacyId: "4190",
    role: "shift leader",
    name: "DAHBI, HASSAN",
    id: "8046b059-e197-4d3d-9ac6-5b949df4f359",
  },
  {
    legacyId: "1296",
    role: "shift leader",
    name: "RKHAILA, HAMID",
    id: "459f4249-6f31-43bb-afdd-93bf9fbf97bf",
  },
  {
    legacyId: "1055",
    role: "shift leader",
    name: "HAIGOUNE, RACHID",
    id: "b6a59a93-693f-4160-904d-46cec14ac626",
  },
  {
    legacyId: "2221",
    role: "shift leader",
    name: "ELOUARDI, MOHAMMED",
    id: "a0d4cbfe-d78a-4818-964c-87a69f8f9b5b",
  },
  {
    legacyId: "3378",
    role: "shift leader",
    name: "EL OUARDI, MOHAMED",
    id: "27076217-9a0c-45a1-82ec-7383563c7ee7",
  },
  {
    legacyId: "48",
    role: "shift leader",
    name: "ACHOURFI, KHALID",
    id: "2e6b0dd3-f6f0-46cc-8354-53fd1793d56a",
  },
  {
    legacyId: "69",
    role: "shift leader",
    name: "CHAD, ABDENBI",
    id: "1254ff03-e6bd-40ca-bfd9-c8ac5ce87d87",
  },
  {
    legacyId: "97",
    role: "shift leader",
    name: "LAMINE, MUSTAPHA",
    id: "7ec242d7-aef4-4786-9081-784d5c03e3a8",
  },
  {
    legacyId: "1178",
    role: "team leader",
    name: "AOUISS, YOUSSEF",
    id: "ea6229b5-9039-43ea-9605-99b4871efb69",
  },
  {
    legacyId: "3532",
    role: "tks agent",
    name: "OURIOUR, MARIA",
    id: "7f6d4e75-5660-4c7c-9d86-7effca3eca3b",
  },
  {
    legacyId: "808",
    role: "department clerk",
    name: "EL GHAZI, NEZHA",
    id: "ea2a521f-2504-4f5c-9800-d96ebbbac8fa",
  },
  {
    legacyId: "773",
    role: "department clerk",
    name: "AMAMOU, TOURIA",
    id: "f5914303-c80c-430d-b5de-cb328d5d6f91",
  },
  {
    legacyId: "622",
    role: "employee is/ih",
    name: "TAMAHMACHTE, SAID",
    id: "27b72d01-4c57-4725-beaf-2e17994bd96e",
  },
  {
    legacyId: "2325",
    role: "employee is/ih",
    name: "KORCHI, MOHCINE",
    id: "b310e30b-84bd-421d-965a-fb29ad1ee126",
  },
  {
    legacyId: "733",
    role: "quality supervisor",
    name: "HAIDOUD, CHRIF",
    id: "60b906d2-36e2-4974-a835-e46f87c72925",
  },
  {
    legacyId: "1203",
    role: "quality supervisor",
    name: "ASBAA, MOHAMED",
    id: "1a9bb044-1bfe-461f-93de-f4ad4ccc574f",
  },
  {
    legacyId: "1204",
    role: "quality auditor",
    name: "BADER, MOHAMED",
    id: "1a9bb044-1bfe-461f-93de-f4ad4ccc575f",
  },
  {
    legacyId: "1205",
    role: "department clerk",
    name: "CLERK, DEPARTMENT",
    id: "2b9bb044-1bfe-461f-93de-f4ad4ccc576f",
  },
];

// Helper function to get mock user by ID
export const getMockUserById = (userId: string): SimpleMockUser | undefined => {
  return mockUsers.find((user) => user.id === userId);
};

// Helper function to get all available mock users
export const getAllMockUsers = (): SimpleMockUser[] => {
  return mockUsers;
};
